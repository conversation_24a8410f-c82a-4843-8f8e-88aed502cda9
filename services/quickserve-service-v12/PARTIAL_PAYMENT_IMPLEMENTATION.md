# Automatic Partial Payment Implementation - Complete Guide

## 🚨 **Issues Addressed**

### **Issue 1: payment_transfered table rounding**
✅ **ALREADY FIXED** - Line 1801 uses exact amount without rounding:
```php
'amount' => $transferAmount, // Use exact amount without rounding
```

### **Issue 2: Automatic Partial Payment Support**
✅ **NOW IMPLEMENTED** - Intelligent automatic partial payment logic

---

## 🔧 **Implementation Details**

### **1. Simple Client Request**
```php
// Only payment_method field required - system handles the rest automatically
'payment_method' => 'sometimes|string|in:online,wallet',
```

### **2. Automatic Payment Logic**

#### **handleAutomaticWalletPayment()**
- Automatically checks wallet balance
- Intelligently determines payment split
- No manual calculation required from client

### **3. Automatic Payment Flow**

#### **Scenario 1: Full Online Payment**
```json
{
  "payment_method": "online"
  // System automatically: wallet_amount_used = 0, gateway_amount = total
}
```

#### **Scenario 2: Full Wallet Payment (Sufficient Balance)**
```json
{
  "payment_method": "wallet"
  // System checks: wallet_balance >= order_total
  // Result: wallet_amount_used = total, gateway_amount = 0
}
```

#### **Scenario 3: Automatic Partial Payment (Insufficient Wallet)**
```json
{
  "payment_method": "wallet"
  // System checks: wallet_balance < order_total
  // Result: wallet_amount_used = wallet_balance, gateway_amount = remaining
}
```

---

## 🧪 **Testing with cURL**

### **Test Case 1: Automatic Wallet Payment (Will Auto-Determine Partial if Needed)**

```bash
curl -X POST "http://*************:8003/api/v2/order-management/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "customer_id": 3187,
    "user_id": 1,
    "company_id": 8163,
    "unit_id": 1,
    "fk_kitchen_code": 1,
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "7738039366",
    "customer_address": "123 Main St",
    "location_code": 101,
    "location_name": "Downtown",
    "city": 1,
    "city_name": "Mumbai",
    "start_date": "2025-01-06",
    "selected_days": [1,2,3,4,5],
    "subscription_days": 5,
    "meals": [
      {
        "product_code": 101,
        "quantity": 1
      }
    ],
    "payment_method": "wallet",
    "food_preference": "veg"
  }'
```

### **Test Case 2: Full Online Payment**

```bash
curl -X POST "http://*************:8003/api/v2/order-management/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "customer_id": 3187,
    "user_id": 1,
    "company_id": 8163,
    "unit_id": 1,
    "fk_kitchen_code": 1,
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "7738039366",
    "customer_address": "123 Main St",
    "location_code": 101,
    "location_name": "Downtown",
    "city": 1,
    "city_name": "Mumbai",
    "start_date": "2025-01-06",
    "selected_days": [1,2,3,4,5],
    "subscription_days": 5,
    "meals": [
      {
        "product_code": 101,
        "quantity": 1
      }
    ],
    "payment_method": "online",
    "food_preference": "veg"
  }'
```

### **Test Case 3: Express Order with Automatic Wallet Logic**

```bash
curl -X POST "http://*************:8003/api/v2/order-management/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "customer_id": 3187,
    "user_id": 1,
    "company_id": 8163,
    "unit_id": 1,
    "fk_kitchen_code": 1,
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "7738039366",
    "customer_address": "123 Main St",
    "location_code": 101,
    "location_name": "Downtown",
    "city": 1,
    "city_name": "Mumbai",
    "start_date": "2025-01-06",
    "selected_days": [1,2,3,4,5],
    "subscription_days": 5,
    "meals": [
      {
        "product_code": 101,
        "quantity": 1
      }
    ],
    "is_express": true,
    "payment_method": "wallet",
    "food_preference": "veg"
  }'
```

---

## 📊 **Expected Database Entries**

### **payment_transaction Table**
```sql
INSERT INTO payment_transaction (
    payment_amount,     -- Gateway amount only
    wallet_amount,      -- Wallet amount used
    transaction_charges, -- Based on gateway amount
    context,            -- 'partial_payment', 'wallet_payment', or 'order_payment'
    status,             -- 'initiated' for partial/online, 'completed' for wallet
    gateway             -- 'initiated' for partial/online, 'wallet' for wallet
);
```

### **payment_transfered Table**
```sql
INSERT INTO payment_transfered (
    amount,             -- payment_amount + transaction_charges (EXACT, no rounding)
    source,             -- payment_service_transaction_id
    recipient,          -- merchant account
    currency            -- 'INR'
);
```

### **customer_wallet Table**
```sql
-- Credit entry for gateway payment
INSERT INTO customer_wallet (
    wallet_amount,      -- payment_amount
    amount_type,        -- 'cr'
    description         -- Payment received
);

-- Debit entry for wallet usage
INSERT INTO customer_wallet (
    wallet_amount,      -- wallet_amount_used
    amount_type,        -- 'dr'
    description         -- Amount deducted for order
);
```

---

## 🔍 **Validation Logic**

### **1. Wallet Balance Check**
- Validates wallet balance >= wallet_amount_used
- Returns error if insufficient balance

### **2. Amount Validation**
- For partial payments: wallet_amount_used + gateway_amount = order_total
- Tolerance of 1 paisa for rounding differences
- Returns error if amounts don't match

### **3. Payment Method Logic**
- `partial`: Both wallet and gateway amounts > 0
- `wallet`: Only wallet amount > 0, gateway = 0
- `online`: Only gateway amount > 0, wallet = 0

---

## 🚀 **Response Examples**

### **Successful Full Wallet Payment Response**
```json
{
  "success": true,
  "message": "Pre-order created successfully",
  "data": {
    "order_no": "ORD_20250106_001",
    "payment_service_transaction_id": null,
    "total_amount": 393.75,
    "wallet_amount_used": 393.75,
    "gateway_amount": 0,
    "payment_method_final": "wallet",
    "temp_pre_order_ids": [1001, 1002],
    "payment_url": null
  }
}
```

### **Successful Automatic Partial Payment Response**
```json
{
  "success": true,
  "message": "Pre-order created successfully",
  "data": {
    "order_no": "ORD_20250106_001",
    "payment_service_transaction_id": "txn_123456789",
    "total_amount": 393.75,
    "wallet_amount_used": 200.00,
    "gateway_amount": 193.75,
    "payment_method_final": "partial",
    "temp_pre_order_ids": [1001, 1002],
    "payment_url": "https://payment-gateway.com/pay/txn_123456789"
  }
}
```

### **Successful Online Payment Response**
```json
{
  "success": true,
  "message": "Pre-order created successfully",
  "data": {
    "order_no": "ORD_20250106_001",
    "payment_service_transaction_id": "txn_123456789",
    "total_amount": 393.75,
    "wallet_amount_used": 0,
    "gateway_amount": 393.75,
    "payment_method_final": "online",
    "temp_pre_order_ids": [1001, 1002],
    "payment_url": "https://payment-gateway.com/pay/txn_123456789"
  }
}
```

---

## 🔄 **Payment Flow Integration**

### **Current Flow (Unchanged):**
1. **Order Creation** → `order-management/create`
2. **Payment Processing** → Payment Service V12
3. **Gateway Integration** → Razorpay SDK (mobile)
4. **Payment Callback** → `payment/callback`

### **New Automatic Logic:**
- **Client sends**: Only `payment_method: "wallet"` or `payment_method: "online"`
- **System determines**: Automatic wallet usage and gateway amounts
- **No client changes**: Existing mobile app works without modification

## 🎯 **Client-Side Impact**

### **✅ No Changes Required:**
- Existing mobile app continues to work
- Same API endpoints
- Same request format
- Same response structure

### **✅ Enhanced UX:**
- Automatic partial payment when wallet insufficient
- No manual amount calculation required
- Seamless user experience

## ✅ **Implementation Status**

- ✅ Automatic wallet payment logic implemented
- ✅ Intelligent partial payment determination
- ✅ No client-side changes required
- ✅ Maintains existing API compatibility
- ✅ Database entries for all payment types
- ✅ OpenAPI specification updated
- ✅ Error handling for all scenarios
- ✅ Logging for debugging
- ✅ payment_transfered exact amounts (no rounding)

## 🎯 **Ready for Testing**

The automatic partial payment functionality is now fully implemented and ready for testing with the provided cURL commands. The system intelligently handles wallet balance and automatically determines the best payment method without requiring any client-side changes.
