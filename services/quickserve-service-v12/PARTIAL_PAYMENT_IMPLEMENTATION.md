# Partial Payment Implementation - Complete Guide

## 🚨 **Issues Addressed**

### **Issue 1: payment_transfered table rounding**
✅ **ALREADY FIXED** - Line 1801 uses exact amount without rounding:
```php
'amount' => $transferAmount, // Use exact amount without rounding
```

### **Issue 2: Partial Payment Support**
✅ **NOW IMPLEMENTED** - Complete partial payment logic added

---

## 🔧 **Implementation Details**

### **1. New Validation Fields Added**
```php
// Payment method and partial payment support
'payment_method' => 'sometimes|string|in:online,wallet,partial',
'wallet_amount_used' => 'sometimes|numeric|min:0',
'gateway_amount' => 'sometimes|numeric|min:0',
'payment_method_final' => 'sometimes|string|in:online,wallet,partial',
```

### **2. New Methods Added**

#### **handlePartialPaymentLogic()**
- Validates wallet balance against requested wallet amount
- Determines final payment method based on input
- Handles all payment scenarios (full wallet, full online, partial)

#### **validatePaymentAmounts()**
- Validates that wallet + gateway amounts equal order total
- Ensures payment consistency
- Handles tolerance for rounding differences (1 paisa)

### **3. Payment Logic Flow**

#### **Scenario 1: Full Online Payment**
```json
{
  "payment_method": "online",
  "wallet_amount_used": 0,
  "gateway_amount": 393.75
}
```

#### **Scenario 2: Full Wallet Payment**
```json
{
  "payment_method": "wallet",
  "wallet_amount_used": 393.75,
  "gateway_amount": 0
}
```

#### **Scenario 3: Partial Payment**
```json
{
  "payment_method": "partial",
  "wallet_amount_used": 200.00,
  "gateway_amount": 193.75
}
```

---

## 🧪 **Testing with cURL**

### **Test Case 1: Partial Payment (Wallet + Gateway)**

```bash
curl -X POST "http://*************:8003/api/v2/order-management/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "customer_id": 3187,
    "user_id": 1,
    "company_id": 8163,
    "unit_id": 1,
    "fk_kitchen_code": 1,
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "7738039366",
    "customer_address": "123 Main St",
    "location_code": 101,
    "location_name": "Downtown",
    "city": 1,
    "city_name": "Mumbai",
    "start_date": "2025-01-06",
    "selected_days": [1,2,3,4,5],
    "subscription_days": 5,
    "meals": [
      {
        "product_code": 101,
        "quantity": 1
      }
    ],
    "payment_method": "partial",
    "wallet_amount_used": 200.00,
    "gateway_amount": 193.75,
    "food_preference": "veg"
  }'
```

### **Test Case 2: Full Wallet Payment**

```bash
curl -X POST "http://*************:8003/api/v2/order-management/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "customer_id": 3187,
    "user_id": 1,
    "company_id": 8163,
    "unit_id": 1,
    "fk_kitchen_code": 1,
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "7738039366",
    "customer_address": "123 Main St",
    "location_code": 101,
    "location_name": "Downtown",
    "city": 1,
    "city_name": "Mumbai",
    "start_date": "2025-01-06",
    "selected_days": [1,2,3,4,5],
    "subscription_days": 5,
    "meals": [
      {
        "product_code": 101,
        "quantity": 1
      }
    ],
    "payment_method": "wallet",
    "wallet_amount_used": 393.75,
    "food_preference": "veg"
  }'
```

### **Test Case 3: Express Order with Partial Payment**

```bash
curl -X POST "http://*************:8003/api/v2/order-management/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "customer_id": 3187,
    "user_id": 1,
    "company_id": 8163,
    "unit_id": 1,
    "fk_kitchen_code": 1,
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "7738039366",
    "customer_address": "123 Main St",
    "location_code": 101,
    "location_name": "Downtown",
    "city": 1,
    "city_name": "Mumbai",
    "start_date": "2025-01-06",
    "selected_days": [1,2,3,4,5],
    "subscription_days": 5,
    "meals": [
      {
        "product_code": 101,
        "quantity": 1
      }
    ],
    "is_express": true,
    "payment_method": "partial",
    "wallet_amount_used": 250.00,
    "gateway_amount": 233.75,
    "food_preference": "veg"
  }'
```

---

## 📊 **Expected Database Entries**

### **payment_transaction Table**
```sql
INSERT INTO payment_transaction (
    payment_amount,     -- Gateway amount only
    wallet_amount,      -- Wallet amount used
    transaction_charges, -- Based on gateway amount
    context,            -- 'partial_payment', 'wallet_payment', or 'order_payment'
    status,             -- 'initiated' for partial/online, 'completed' for wallet
    gateway             -- 'initiated' for partial/online, 'wallet' for wallet
);
```

### **payment_transfered Table**
```sql
INSERT INTO payment_transfered (
    amount,             -- payment_amount + transaction_charges (EXACT, no rounding)
    source,             -- payment_service_transaction_id
    recipient,          -- merchant account
    currency            -- 'INR'
);
```

### **customer_wallet Table**
```sql
-- Credit entry for gateway payment
INSERT INTO customer_wallet (
    wallet_amount,      -- payment_amount
    amount_type,        -- 'cr'
    description         -- Payment received
);

-- Debit entry for wallet usage
INSERT INTO customer_wallet (
    wallet_amount,      -- wallet_amount_used
    amount_type,        -- 'dr'
    description         -- Amount deducted for order
);
```

---

## 🔍 **Validation Logic**

### **1. Wallet Balance Check**
- Validates wallet balance >= wallet_amount_used
- Returns error if insufficient balance

### **2. Amount Validation**
- For partial payments: wallet_amount_used + gateway_amount = order_total
- Tolerance of 1 paisa for rounding differences
- Returns error if amounts don't match

### **3. Payment Method Logic**
- `partial`: Both wallet and gateway amounts > 0
- `wallet`: Only wallet amount > 0, gateway = 0
- `online`: Only gateway amount > 0, wallet = 0

---

## 🚀 **Response Examples**

### **Successful Partial Payment Response**
```json
{
  "success": true,
  "message": "Pre-order created successfully",
  "data": {
    "order_no": "ORD_20250106_001",
    "payment_service_transaction_id": "txn_123456789",
    "total_amount": 393.75,
    "wallet_amount_used": 200.00,
    "gateway_amount": 193.75,
    "payment_method_final": "partial",
    "temp_pre_order_ids": [1001, 1002],
    "payment_url": "https://payment-gateway.com/pay/txn_123456789"
  }
}
```

### **Insufficient Wallet Balance Error**
```json
{
  "success": false,
  "message": "Insufficient wallet balance for partial payment",
  "data": {
    "wallet_balance": 150.00,
    "wallet_amount_requested": 200.00,
    "shortfall": 50.00
  }
}
```

### **Amount Mismatch Error**
```json
{
  "success": false,
  "message": "Payment amounts do not match order total",
  "data": {
    "total_order_amount": 393.75,
    "wallet_amount_used": 200.00,
    "gateway_amount": 190.00,
    "total_payment_amount": 390.00,
    "difference": 3.75
  }
}
```

---

## ✅ **Implementation Status**

- ✅ Validation fields added
- ✅ Partial payment logic implemented
- ✅ Wallet balance validation
- ✅ Amount validation with tolerance
- ✅ Database entries for all payment types
- ✅ OpenAPI specification updated
- ✅ Error handling for all scenarios
- ✅ Logging for debugging
- ✅ payment_transfered exact amounts (no rounding)

## 🎯 **Ready for Testing**

The partial payment functionality is now fully implemented and ready for testing with the provided cURL commands. The system supports all three payment scenarios: full online, full wallet, and partial payments with proper validation and error handling.
