openapi: 3.0.3
info:
  title: Order Management API
  description: |
    Comprehensive order management system for QuickServe food delivery platform with advanced cancellation policies and order swap functionality.

    ## Complete Order Journey
    1. **Create Pre-Order** - Creates temp_pre_orders, temp_order_payment, payment_transaction (initiated)
    2. **Payment Processing** - Mobile app uses Payment Service v12 APIs
    3. **Payment Success** - Updates payment_transaction, creates payment_transfered (Razorpay), updates temp_order_payment
    4. **Order Creation** - Creates multiple orders (15 days = 12 weekday orders) and order_details (3 items × 12 orders = 36 records)
    5. **Wallet Locking** - Locks wallet amount for each order for cancellation tracking
    6. **Order Fulfillment** - All orders ready for delivery with status "New" (not "Confirmed")

    ## Advanced Cancellation System
    ### Time-Based Refund Policies:
    1. **Before Cutoff Time** → 100% refund + unlock wallet amount
    2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
    3. **After 08:00:00** → No cancellation allowed

    ## Order Swap System
    - Allows customers to swap meals within the same category
    - Automatic price difference calculation and tax recalculation
    - Comprehensive validation and audit logging

  version: 2.1.0
  contact:
    name: QuickServe API Support
    email: <EMAIL>

servers:
  - url: http://************:8000/api/v2
    description: Development server
  - url: http://*************:8000/v2
    description: Production server
  - url: http://**************:9002/api/v2
    description: Staging server

security:
  - BearerAuth: []

paths:
  /order-management/create:
    post:
      tags:
        - Order Management
      summary: Create new order with payment integration (Normal and Express)
      description: |
        Creates a new order with temporary tables and payment integration.
        Returns payment_service_transaction_id for mobile app payment processing.
        
        Normal Orders:
        - Supports two models:
          1) Repeating model using `start_date`, `selected_days`, `subscription_days`, and a single `meals` cart that repeats.
          2) Per-date heterogeneous model using `meals_by_date` to schedule different meals per specific date.
        
        Express Orders:
        - Set `is_express=true`. System evaluates meal-specific cutoff.
        - If DB cutoff is `00:00:00` (midnight), same-day express allowed until `08:00:00` (configurable) with extra delivery charge.
        - Extra delivery charge is per kitchen and meal via setting `K{KITCHEN_ID}_{MEALTYPE}_EXPRESS_EXTRA_DELIVERY_CHARGE`.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/NormalCreateOrderRequest'
                - $ref: '#/components/schemas/ExpressCreateOrderRequest'
              discriminator:
                propertyName: is_express
                mapping:
                  false: '#/components/schemas/NormalCreateOrderRequest'
                  true: '#/components/schemas/ExpressCreateOrderRequest'
            examples:
              normal_repeating:
                summary: Normal order - repeating schedule
                value:
                  customer_id: 3187
                  user_id: 1
                  company_id: 8163
                  unit_id: 1
                  fk_kitchen_code: 1
                  customer_name: "John Doe"
                  customer_email: "<EMAIL>"
                  customer_phone: "7738039366"
                  customer_address: "123 Main St"
                  location_code: 101
                  location_name: "."
                  city: 1
                  city_name: "City"
                  start_date: "2025-08-25"
                  selected_days: [1,2,3,4,5]
                  subscription_days: 10
                  meals:
                    - product_code: 101
                      quantity: 1
              normal_per_date:
                summary: Normal order - per-date heterogeneous meals
                value:
                  customer_id: 3187
                  user_id: 1
                  company_id: 8163
                  unit_id: 1
                  fk_kitchen_code: 1
                  customer_name: "John Doe"
                  customer_email: "<EMAIL>"
                  customer_phone: "7738039366"
                  customer_address: "123 Main St"
                  location_code: 101
                  location_name: "."
                  city: 1
                  city_name: "City"
                  meals_by_date:
                    - date: "2025-08-25"
                      meals:
                        - product_code: 101
                          quantity: 1
                    - date: "2025-08-26"
                      meals:
                        - product_code: 201
                          quantity: 1
              partial_payment:
                summary: Partial payment - wallet + gateway
                value:
                  customer_id: 3187
                  user_id: 1
                  company_id: 8163
                  unit_id: 1
                  fk_kitchen_code: 1
                  customer_name: "John Doe"
                  customer_email: "<EMAIL>"
                  customer_phone: "7738039366"
                  customer_address: "123 Main St"
                  location_code: 101
                  location_name: "."
                  city: 1
                  city_name: "City"
                  start_date: "2025-08-25"
                  selected_days: [1,2,3,4,5]
                  subscription_days: 10
                  meals:
                    - product_code: 101
                      quantity: 1
                  payment_method: "partial"
                  wallet_amount_used: 200.00
                  gateway_amount: 193.75
              wallet_payment:
                summary: Full wallet payment
                value:
                  customer_id: 3187
                  user_id: 1
                  company_id: 8163
                  unit_id: 1
                  fk_kitchen_code: 1
                  customer_name: "John Doe"
                  customer_email: "<EMAIL>"
                  customer_phone: "7738039366"
                  customer_address: "123 Main St"
                  location_code: 101
                  location_name: "."
                  city: 1
                  city_name: "City"
                  start_date: "2025-08-25"
                  selected_days: [1,2,3,4,5]
                  subscription_days: 10
                  meals:
                    - product_code: 101
                      quantity: 1
                  payment_method: "wallet"
                  wallet_amount_used: 393.75
              express_per_date:
                summary: Express order - per-date today
                value:
                  is_express: true
                  customer_id: 3187
                  user_id: 1
                  company_id: 8163
                  unit_id: 1
                  fk_kitchen_code: 1
                  customer_name: "John Doe"
                  customer_email: "<EMAIL>"
                  customer_phone: "7738039366"
                  customer_address: "123 Main St"
                  location_code: 101
                  location_name: "."
                  city: 1
                  city_name: "City"
                  meals_by_date:
                    - date: "2025-08-22"
                      meals:
                        - product_code: 201
                          quantity: 1
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
              examples:
                multiple_meals_same_type:
                  summary: More than one meal item for same meal type on same date
                  value:
                    success: false
                    message: "Only one menu item can be added per meal type for this day"
                    meal_type: "breakfast"
                    date: "2025-08-25"

  /order-management/cancel/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Cancel order with time-based refund processing
      description: |
        Advanced order cancellation with time-based refund policies and wallet management.
        Includes cancellation tracking with user details and timestamps.
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to cancel
          example: "**********"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelOrderRequest'
      responses:
        '200':
          description: Order cancelled successfully with refund processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelOrderResponse'
        '400':
          description: Invalid request - orders cannot be cancelled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: No eligible orders found for cancellation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/swap/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Swap order product with another product from the same category
      description: |
        Allows customers to swap their meal with another product from the same category.
        Updates both orders and order_details tables while maintaining data integrity.

        **Key Features:**
        - Product category validation - Only same category swaps allowed
        - Order status validation - Only swappable orders (not delivered/cancelled)
        - Price difference calculation - Automatic price adjustment
        - Swap charges support - Additional charges for premium swaps
        - Tax recalculation - Updated tax based on new amount
        - Audit logging - Complete swap history tracking

        **Price Calculation:**
        ```
        New Amount = Old Amount + Price Difference + Swap Charges
        ```
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to swap
          example: "**********"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwapOrderRequest'
      responses:
        '200':
          description: Order swapped successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwapOrderResponse'
        '400':
          description: Invalid request - order not swappable or products not compatible
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order or product not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/customer/{customerCode}:
    get:
      tags:
        - Order Management
      summary: Get customer orders with cancellation details
      description: |
        Retrieves all orders for a specific customer including cancellation details
        such as cancelled by whom and cancellation date.
      parameters:
        - name: customerCode
          in: path
          required: true
          schema:
            type: integer
          description: Customer code
          example: 1
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
          description: Company id (required) - must be provided as query parameter for GET requests
        - name: include_cancelled
          in: query
          schema:
            type: boolean
            default: false
          description: Include cancelled orders in response (accepts true/false/1/0/on/off)
        - name: student_name_filter
          in: query
          schema:
            type: string
            maxLength: 250
          description: Filter orders where ship_address starts with this value (student name prefix)
          example: "Aarav"
        - name: order_status
          in: query
          schema:
            type: string
            enum: [New, Confirmed, Processing, Prepared, Dispatched, Delivered, Cancelled]
          description: Filter by order status
        - name: start_date
          in: query
          schema:
            type: string
            format: date
          description: Inclusive start date (YYYY-MM-DD) for filtering by order_date
          example: "2025-01-01"
        - name: end_date
          in: query
          schema:
            type: string
            format: date
          description: Inclusive end date (YYYY-MM-DD) for filtering by order_date
          example: "2025-01-31"
        - name: per_page
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          description: Page size for the combined orders list ("all")
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number for the combined orders list ("all")
      responses:
        '200':
          description: Customer orders retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrdersResponseV2'
  /order-management/details/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Get order details with payment status
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to retrieve details for
          example: "**********"
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
          description: Company id (required) - must be provided as query parameter for GET requests
      responses:
        '200':
          description: Order details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/pre-order-status/{orderNo}:
    get:
      tags:
        - Order Management
      summary: Check pre-order status for a given order number
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Pre-order number to check status
          example: "**********"
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
          description: Company id (required) - must be provided as query parameter for GET requests
      responses:
        '200':
          description: Pre-order status
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  status:
                    type: string
        '404':
          description: Pre-order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/payment-success/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Payment success callback from payment service
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number for payment callback
          example: "**********"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentCallbackRequest'
      responses:
        '200':
          description: Payment success handled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/payment-failure/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Payment failure callback from payment service
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number for payment callback
          example: "**********"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentCallbackRequest'
      responses:
        '200':
          description: Payment failure handled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/apply-coupon:
    post:
      tags:
        - Order Management
      summary: Apply coupon to pre-order or order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplyCouponRequest'
      responses:
        '200':
          description: Coupon applied
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

components:
  schemas:
    NormalCreateOrderRequest:
      type: object
      description: Normal (non-express) order create request. Supports repeating schedule or per-date heterogeneous meals.
      allOf:
        - $ref: '#/components/schemas/CreateOrderBase'
        - type: object
          properties:
            is_express:
              type: boolean
              default: false
          required:
            - customer_id
            - user_id
            - company_id
            - unit_id
            - fk_kitchen_code
            - customer_address
            - location_code
            - location_name
            - city
            - city_name
      oneOf:
        - $ref: '#/components/schemas/NormalRepeatingModel'
        - $ref: '#/components/schemas/NormalPerDateModel'

    ExpressCreateOrderRequest:
      type: object
      description: Express order create request. Same as Normal but requires is_express=true.
      allOf:
        - $ref: '#/components/schemas/NormalCreateOrderRequest'
        - type: object
          properties:
            is_express:
              type: boolean
              enum: [true]
          required: [is_express]

    CreateOrderBase:
      type: object
      properties:
        customer_id:
          type: integer
        user_id:
          type: integer
        company_id:
          type: integer
        unit_id:
          type: integer
        fk_kitchen_code:
          type: integer
        customer_name:
          type: string
        customer_email:
          type: string
          format: email
        customer_phone:
          type: string
        customer_address:
          type: string
          maxLength: 1000
        location_code:
          type: integer
        location_name:
          type: string
          maxLength: 100
        city:
          type: integer
        city_name:
          type: string
          maxLength: 100
        food_preference:
          type: string
          maxLength: 50
        # Payment fields for partial payment support
        payment_method:
          type: string
          enum: [online, wallet, partial]
          default: online
          description: |
            Payment method:
            - online: Full payment via gateway
            - wallet: Full payment from wallet
            - partial: Split payment (wallet + gateway)
        wallet_amount_used:
          type: number
          format: float
          minimum: 0
          description: Amount to be deducted from wallet (required for wallet/partial payments)
        gateway_amount:
          type: number
          format: float
          minimum: 0
          description: Amount to be paid via gateway (required for online/partial payments)
        payment_method_final:
          type: string
          enum: [online, wallet, partial]
          description: Final payment method determined by the system (read-only)

    NormalRepeatingModel:
      type: object
      required: [meals, start_date, selected_days, subscription_days]
      properties:
        meals:
          type: array
          items:
            $ref: '#/components/schemas/MealCartItemShort'
        start_date:
          type: string
          format: date
        selected_days:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
        subscription_days:
          type: integer
          minimum: 1
          maximum: 300

    NormalPerDateModel:
      type: object
      required: [meals_by_date]
      properties:
        meals_by_date:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/MealsByDateEntry'

    MealsByDateEntry:
      type: object
      required: [date, meals]
      properties:
        date:
          type: string
          format: date
          description: Delivery date (YYYY-MM-DD), must be today or a future date.
        meals:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/PerDateMealItem'
      description: A specific date with its own set of meals.

    PerDateMealItem:
      type: object
      required: [product_code, quantity]
      properties:
        product_code:
          type: integer
        quantity:
          type: integer
          minimum: 1

    MealCartItemShort:
      type: object
      required: [product_code, quantity]
      properties:
        product_code:
          type: integer
        quantity:
          type: integer
          minimum: 1
    CreateOrderRequest:
      deprecated: true
      description: Deprecated legacy schema. Use NormalCreateOrderRequest or ExpressCreateOrderRequest.
      allOf:
        - $ref: '#/components/schemas/NormalCreateOrderRequest'

    MealCartItem:
      type: object
      required:
        - product_code
        - product_name
        - quantity
        - amount
      properties:
        product_code:
          type: integer
          description: Product code (details fetched from products table)
          example: 339
        product_name:
          type: string
          description: Name of the product
          example: "Indian Breakfast"
        quantity:
          type: integer
          minimum: 1
          description: Quantity of this meal
          example: 1
        amount:
          type: number
          format: float
          description: Price for this quantity of the product
          example: 75.00

    CreateOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order created successfully"
        data:
          type: object
          properties:
            primary_order_no:
              type: string
              example: "**********"
            payment_service_transaction_id:
              type: string
              example: "txn_abc123def456"
            temp_pre_order_id:
              type: integer
              example: 12345
            express:
              type: object
              nullable: true
              description: "Present when is_express=true"
              properties:
                evaluated:
                  type: boolean
                  example: true
                within_window:
                  type: boolean
                  example: true
                cutoff_time:
                  type: string
                  example: "00:00:00"
                extended_end_time:
                  type: string
                  example: "08:00:00"
                extra_delivery_charge:
                  type: number
                  format: decimal
                  example: 40.00

    CancelOrderRequest:
      type: object
      required:
        - reason
        - cancel_dates
        - company_id
      properties:
        company_id:
          type: integer
          description: Company id associated with this request
          example: 8163
        reason:
          type: string
          maxLength: 500
          description: "Reason for order cancellation"
          example: "Customer requested cancellation due to change in plans"
        cancel_dates:
          type: array
          minItems: 1
          items:
            type: string
            format: date
          description: "Specific dates to cancel"
          example: ["2025-09-03", "2025-09-10"]
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional: Filter cancellation by specific meal type"
          example: "lunch"
        cancelled_by:
          type: string
          description: "Who initiated the cancellation"
          example: "customer"
          enum: [customer, admin, system]

    CancelOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Orders cancelled successfully with time-based refund policy"
        data:
          type: object
          properties:
            cancelled_orders:
              type: integer
              description: "Number of orders cancelled"
              example: 3
            cancelled_order_ids:
              type: array
              items:
                type: integer
              description: "List of cancelled order IDs"
              example: [127733, 127734, 127735]
            total_refund_amount:
              type: number
              format: decimal
              description: "Total refund amount credited to wallet"
              example: 125.50
            cancellation_details:
              type: object
              properties:
                cancelled_by:
                  type: string
                  example: "customer"
                cancelled_on:
                  type: string
                  format: date-time
                  example: "2025-07-28T10:30:00Z"
                cancellation_reason:
                  type: string
                  example: "Customer requested cancellation due to change in plans"

    SwapOrderRequest:
      type: object
      required:
        - order_date
        - new_product_code
        - company_id
      properties:
        company_id:
          type: integer
          description: Company id associated with this request
          example: 8163
        order_date:
          type: string
          format: date
          description: "Date of the order to swap (YYYY-MM-DD)"
          example: "2025-09-03"
        new_product_code:
          type: integer
          description: "Product code of the new product to swap to"
          example: 342
        reason:
          type: string
          maxLength: 255
          description: "Optional reason for the swap"
          example: "Customer wants to change from Poha to Upma"
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional filter by meal type"
          example: "breakfast"

    SwapOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order swapped successfully"
        data:
          type: object
          properties:
            order_id:
              type: integer
              description: "Internal order ID"
              example: 127810
            order_no:
              type: string
              description: "Order number that was swapped"
              example: "**********"
            order_date:
              type: string
              format: date
              description: "Date of the swapped order"
              example: "2025-09-03"
            swap_details:
              type: object
              description: "Detailed information about the swap"
              properties:
                old_product:
                  type: object
                  properties:
                    code:
                      type: integer
                      example: 341
                    name:
                      type: string
                      example: "Poha"
                    price:
                      type: number
                      format: decimal
                      example: 150.00
                new_product:
                  type: object
                  properties:
                    code:
                      type: integer
                      example: 342
                    name:
                      type: string
                      example: "Upma"
                    price:
                      type: number
                      format: decimal
                      example: 200.00
                price_difference:
                  type: number
                  format: decimal
                  description: "Price difference between old and new product"
                  example: 50.00
                swap_charges:
                  type: number
                  format: decimal
                  description: "Additional charges for the swap"
                  example: 25.00
                total_amount_change:
                  type: number
                  format: decimal
                  description: "Total amount change (price difference + swap charges)"
                  example: 75.00
                new_order_amount:
                  type: number
                  format: decimal
                  description: "New total order amount after swap"
                  example: 225.00
            reason:
              type: string
              description: "Reason provided for the swap"
              example: "Customer wants to change from Poha to Upma"

    CustomerOrdersResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Customer orders retrieved successfully"

    CustomerOrdersResponseV2:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            customer:
              type: object
              properties:
                customer_id:
                  type: integer
                  example: 1
                name:
                  type: string
                email:
                  type: string
                  format: email
                phone:
                  type: string
            summary:
              type: object
              properties:
                total_orders:
                  type: integer
                upcoming_orders:
                  type: integer
                cancelled_orders:
                  type: integer
                other_orders:
                  type: integer
            student_names:
              type: array
              items:
                type: string
            filters:
              type: object
              properties:
                student_name_filter:
                  type: string
                include_cancelled:
                  type: boolean
                order_status:
                  type: string
                start_date:
                  type: string
                  format: date
                end_date:
                  type: string
                  format: date
                per_page:
                  type: integer
                page_upcoming:
                  type: integer
                page_cancelled:
                  type: integer
                page_other:
                  type: integer
            orders:
              type: object
              properties:
                upcoming:
                  type: array
                  items:
                    $ref: '#/components/schemas/OrderListItem'
                cancelled:
                  type: array
                  items:
                    $ref: '#/components/schemas/OrderListItem'
                other:
                  type: array
                  items:
                    $ref: '#/components/schemas/OrderListItem'
            pagination:
              type: object
              properties:
                upcoming:
                  $ref: '#/components/schemas/PaginationMeta'
                cancelled:
                  $ref: '#/components/schemas/PaginationMeta'
                other:
                  $ref: '#/components/schemas/PaginationMeta'

    OrderListItem:
      type: object
      properties:
        order_id:
          type: integer
        order_no:
          type: string
        order_date:
          type: string
          format: date
        order_status:
          type: string
        delivery_status:
          type: string
        payment_mode:
          type: string
        amount_paid:
          type: number
          format: decimal
        total_amount:
          type: number
          format: decimal
        delivery_time:
          type: string
          nullable: true
        delivery_end_time:
          type: string
          nullable: true
        recurring_status:
          type: integer
        days_preference:
          type: string
        customer_address:
          type: string
        location_name:
          type: string
        city_name:
          type: string
        food_preference:
          type: string
        product_code:
          type: integer
        product_name:
          type: string
        image_path:
          type: string
          nullable: true
        quantity:
          type: integer
        item_amount:
          type: number
          format: decimal
        product_type:
          type: string
        last_modified:
          type: string
          format: date-time

    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        last_page:
          type: integer
        from:
          type: integer
          nullable: true
        to:
          type: integer
          nullable: true
        has_more:
          type: boolean

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"
        data:
          type: object
          nullable: true

    ValidationErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            order_date: ["The order date field is required."]
            new_product_code: ["The new product code field is required."]

    PaymentCallbackRequest:
      type: object
      required:
        - company_id
        - transaction_id
      properties:
        company_id:
          type: integer
          description: Company id associated with this callback
          example: 8163
        transaction_id:
          type: string
          description: Payment gateway transaction id
          example: "txn_abc123def456"
        gateway_response:
          type: object
          description: Optional gateway response payload

    ApplyCouponRequest:
      type: object
      required:
        - company_id
        - order_no
        - promo_code
      properties:
        company_id:
          type: integer
          description: Company id associated with this request
          example: 8163
        order_no:
          type: string
          description: Order number to apply coupon
          example: "**********"
        promo_code:
          type: string
          description: Promo code to apply
          example: "WELCOME10"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "JWT token for authenticated endpoints"

tags:
  - name: Order Management
    description: "Core order management operations including creation, cancellation, and swapping"
